<template>
  <div class="user-page art-full-height">
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :span="4" :xs="24">
        <el-card class="dept-tree-card" shadow="never">
          <template #header>
            <span>部门列表</span>
          </template>
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
          <el-tree
            ref="deptTreeRef"
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            node-key="id"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>

      <!-- 用户列表 -->
      <el-col :span="20" :xs="24">
        <!-- 搜索栏 -->
        <UserSearch v-model="searchForm" @search="handleSearch" @reset="resetSearchParams" />

        <el-card class="art-table-card" shadow="never">
          <!-- 表格头部 -->
          <div class="table-header">
            <div class="table-header-left">
              <el-button
                v-auth="'system:user:add'"
                type="primary"
                icon="Plus"
                @click="showDialog('add')"
              >
                新增用户
              </el-button>
              <el-button
                v-auth="'system:user:edit'"
                type="success"
                icon="Edit"
                :disabled="single"
                @click="handleUpdate"
              >
                修改
              </el-button>
              <el-button
                v-auth="'system:user:remove'"
                type="danger"
                icon="Delete"
                :disabled="multiple"
                @click="handleDelete"
              >
                删除
              </el-button>
              <el-button
                v-auth="'system:user:import'"
                type="info"
                icon="Upload"
                @click="handleImport"
              >
                导入
              </el-button>
              <el-button
                v-auth="'system:user:export'"
                type="warning"
                icon="Download"
                @click="handleExport"
              >
                导出
              </el-button>
            </div>
            <div class="table-header-right">
              <el-button icon="Refresh" circle @click="refreshData" />
            </div>
          </div>

          <!-- 表格 -->
          <el-table
            v-loading="loading"
            :data="userList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column
              label="用户编号"
              align="center"
              prop="userId"
              width="80"
            />
            <el-table-column
              label="用户名称"
              align="center"
              prop="userName"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="用户昵称"
              align="center"
              prop="nickName"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="部门"
              align="center"
              prop="dept.deptName"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="手机号码"
              align="center"
              prop="phonenumber"
              width="120"
            />
            <el-table-column label="状态" align="center" width="100">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="0"
                  inactive-value="1"
                  @change="handleStatusChange(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="创建时间"
              align="center"
              prop="createTime"
              width="160"
            >
              <template #default="scope">
                <span>{{ formatTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="200"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-tooltip content="修改" placement="top" v-if="scope.row.userId !== 1">
                  <el-button
                    v-auth="'system:user:edit'"
                    link
                    type="primary"
                    icon="Edit"
                    @click="handleUpdate(scope.row)"
                  />
                </el-tooltip>
                <el-tooltip content="删除" placement="top" v-if="scope.row.userId !== 1">
                  <el-button
                    v-auth="'system:user:remove'"
                    link
                    type="primary"
                    icon="Delete"
                    @click="handleDelete(scope.row)"
                  />
                </el-tooltip>
                <el-tooltip content="重置密码" placement="top" v-if="scope.row.userId !== 1">
                  <el-button
                    v-auth="'system:user:resetPwd'"
                    link
                    type="primary"
                    icon="Key"
                    @click="handleResetPwd(scope.row)"
                  />
                </el-tooltip>
                <el-tooltip content="分配角色" placement="top" v-if="scope.row.userId !== 1">
                  <el-button
                    v-auth="'system:user:edit'"
                    link
                    type="primary"
                    icon="CircleCheck"
                    @click="handleAuthRole(scope.row)"
                  />
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            v-show="total > 0"
            :total="total"
            :current-page="queryParams.pageNum"
            :page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />

          <!-- 用户弹窗 -->
          <UserForm
            :visible="dialogVisible"
            :type="dialogType"
            :user-data="currentUserData"
            @update:visible="dialogVisible = $event"
            @submit="handleDialogSubmit"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { UserApi } from '@/api/system/user'
import type { User, UserQueryParams } from '@/types/system/user'
import type { Dept } from '@/types/system/dept'
import UserSearch from './components/UserSearch.vue'
import UserForm from './components/UserForm.vue'
import { formatTime } from '@/utils/date'

defineOptions({ name: 'SystemUser' })

// 响应式数据
const loading = ref(true)
const userList = ref<User[]>([])
const total = ref(0)
const single = ref(true)
const multiple = ref(true)
const ids = ref<number[]>([])
const deptName = ref('')
const deptOptions = ref<Dept[]>([])
const deptTreeRef = ref()

// 弹窗相关
const dialogType = ref<'add' | 'edit'>('add')
const dialogVisible = ref(false)
const currentUserData = ref<Partial<User>>({})

// 查询参数
const queryParams = reactive<UserQueryParams>({
  pageNum: 1,
  pageSize: 10,
  userName: undefined,
  phonenumber: undefined,
  status: undefined,
  deptId: undefined
})

// 搜索表单
const searchForm = ref({
  userName: '',
  phonenumber: '',
  status: '',
  beginTime: '',
  endTime: ''
})

// 日期范围
const dateRange = ref<[string, string] | []>([])

/**
 * 查询用户列表
 */
async function getList() {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      beginTime: dateRange.value?.[0],
      endTime: dateRange.value?.[1]
    }
    const response = await UserApi.getUserList(params)
    userList.value = response.rows || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 查询部门下拉树结构
 */
async function getDeptTree() {
  try {
    const response = await UserApi.deptTreeSelect()
    deptOptions.value = response.data || []
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

/**
 * 通过条件过滤节点
 */
function filterNode(value: string, data: Dept) {
  if (!value) return true
  return data.deptName?.indexOf(value) !== -1
}

/**
 * 节点单击事件
 */
function handleNodeClick(data: Dept) {
  queryParams.deptId = data.deptId
  handleQuery()
}

/**
 * 搜索按钮操作
 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/**
 * 重置按钮操作
 */
function resetQuery() {
  dateRange.value = []
  queryParams.deptId = undefined
  deptTreeRef.value?.setCurrentKey(null)
  handleQuery()
}

/**
 * 重置搜索参数
 */
function resetSearchParams() {
  searchForm.value = {
    userName: '',
    phonenumber: '',
    status: '',
    beginTime: '',
    endTime: ''
  }
  dateRange.value = []
  handleQuery()
}

/**
 * 多选框选中数据
 */
function handleSelectionChange(selection: User[]) {
  ids.value = selection.map(item => item.userId!)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/**
 * 新增按钮操作
 */
function handleAdd() {
  showDialog('add')
}

/**
 * 修改按钮操作
 */
function handleUpdate(row?: User) {
  const userId = row?.userId || ids.value[0]
  if (userId) {
    showDialog('edit', { userId })
  }
}

/**
 * 删除按钮操作
 */
async function handleDelete(row?: User) {
  const userIds = row?.userId ? [row.userId] : ids.value
  try {
    await ElMessageBox.confirm(
      `是否确认删除用户编号为"${userIds.join(',')}"的数据项？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    await UserApi.delUser(userIds)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

/**
 * 用户状态修改
 */
async function handleStatusChange(row: User) {
  const text = row.status === '0' ? '启用' : '停用'
  try {
    await ElMessageBox.confirm(
      `确认要"${text}""${row.userName}"用户吗?`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    await UserApi.changeUserStatus(row.userId!, row.status!)
    ElMessage.success(text + '成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改用户状态失败:', error)
      ElMessage.error('修改用户状态失败')
      // 恢复原状态
      row.status = row.status === '0' ? '1' : '0'
    }
  }
}

/**
 * 重置密码
 */
async function handleResetPwd(row: User) {
  try {
    const { value: password } = await ElMessageBox.prompt(
      '请输入"' + row.userName + '"的新密码',
      '重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: '用户密码长度必须介于 5 和 20 之间'
      }
    )
    await UserApi.resetUserPwd(row.userId!, password)
    ElMessage.success('重置成功，新密码是：' + password)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
    }
  }
}

/**
 * 分配角色
 */
function handleAuthRole(row: User) {
  // TODO: 实现角色分配功能
  ElMessage.info('角色分配功能待实现')
}

/**
 * 导入用户
 */
function handleImport() {
  // TODO: 实现用户导入功能
  ElMessage.info('用户导入功能待实现')
}

/**
 * 导出用户
 */
async function handleExport() {
  try {
    const blob = await UserApi.exportUser(queryParams)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `user_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出用户失败:', error)
    ElMessage.error('导出用户失败')
  }
}

/**
 * 显示弹窗
 */
function showDialog(type: 'add' | 'edit', userData?: Partial<User>) {
  dialogType.value = type
  currentUserData.value = userData || {}
  dialogVisible.value = true
}

/**
 * 处理弹窗提交
 */
function handleDialogSubmit() {
  dialogVisible.value = false
  getList()
}

/**
 * 处理搜索
 */
function handleSearch() {
  Object.assign(queryParams, searchForm.value)
  handleQuery()
}

/**
 * 分页大小改变
 */
function handleSizeChange(size: number) {
  queryParams.pageSize = size
  getList()
}

/**
 * 当前页改变
 */
function handleCurrentChange(page: number) {
  queryParams.pageNum = page
  getList()
}

/**
 * 刷新数据
 */
function refreshData() {
  getList()
}

// 监听部门名称变化，过滤部门树
watch(deptName, (val) => {
  deptTreeRef.value?.filter(val)
})

// 组件挂载时获取数据
onMounted(() => {
  getList()
  getDeptTree()
})
</script>

<style lang="scss" scoped>
.user-page {
  .dept-tree-card {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    &-left {
      display: flex;
      gap: 8px;
    }

    &-right {
      display: flex;
      gap: 8px;
    }
  }

  .art-table-card {
    height: calc(100vh - 120px);
    overflow: hidden;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    :deep(.el-table) {
      flex: 1;
    }

    :deep(.el-pagination) {
      margin-top: 16px;
      justify-content: center;
    }
  }
}
</style>
