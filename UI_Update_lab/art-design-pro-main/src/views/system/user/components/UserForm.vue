<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="600px"
    append-to-body
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户昵称" prop="nickName">
            <el-input
              v-model="form.nickName"
              placeholder="请输入用户昵称"
              maxlength="30"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="归属部门" prop="deptId">
            <el-tree-select
              v-model="form.deptId"
              :data="deptOptions"
              :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
              value-key="deptId"
              placeholder="请选择归属部门"
              clearable
              check-strictly
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="手机号码" prop="phonenumber">
            <el-input
              v-model="form.phonenumber"
              placeholder="请输入手机号码"
              maxlength="11"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="form.email"
              placeholder="请输入邮箱"
              maxlength="50"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item v-if="!form.userId" label="用户名称" prop="userName">
            <el-input
              v-model="form.userName"
              placeholder="请输入用户名称"
              maxlength="30"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="!form.userId" label="用户密码" prop="password">
            <el-input
              v-model="form.password"
              placeholder="请输入用户密码"
              type="password"
              maxlength="20"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户性别">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option label="男" value="0" />
              <el-option label="女" value="1" />
              <el-option label="未知" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态">
            <el-radio-group v-model="form.status">
              <el-radio value="0">正常</el-radio>
              <el-radio value="1">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="岗位">
            <el-select v-model="form.postIds" multiple placeholder="请选择">
              <el-option
                v-for="item in postOptions"
                :key="item.postId"
                :label="item.postName"
                :value="item.postId"
                :disabled="item.status === '1'"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="角色">
            <el-select v-model="form.roleIds" multiple placeholder="请选择">
              <el-option
                v-for="item in roleOptions"
                :key="item.roleId"
                :label="item.roleName"
                :value="item.roleId"
                :disabled="item.status === '1'"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { UserApi } from '@/api/system/user'
import type { User } from '@/types/system/user'
import type { Dept } from '@/types/system/dept'
import type { Role } from '@/types/system/role'
import type { Post } from '@/types/system/post'

interface Props {
  visible: boolean
  type: 'add' | 'edit'
  userData?: Partial<User>
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'submit'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const deptOptions = ref<Dept[]>([])
const postOptions = ref<Post[]>([])
const roleOptions = ref<Role[]>([])

// 表单数据
const form = reactive<Partial<User>>({
  userId: undefined,
  deptId: undefined,
  userName: '',
  nickName: '',
  password: '',
  phonenumber: '',
  email: '',
  sex: '0',
  status: '0',
  remark: '',
  postIds: [],
  roleIds: []
})

// 表单验证规则
const rules: FormRules = {
  userName: [
    { required: true, message: '用户名称不能为空', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
  ],
  nickName: [
    { required: true, message: '用户昵称不能为空', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '用户密码不能为空', trigger: 'blur' },
    { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  phonenumber: [
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 弹窗标题
const title = computed(() => {
  return props.type === 'add' ? '添加用户' : '修改用户'
})

// 监听用户数据变化
watch(
  () => props.userData,
  (newVal) => {
    if (newVal && props.visible) {
      Object.assign(form, newVal)
      if (props.type === 'edit' && newVal.userId) {
        getUserDetail(newVal.userId)
      }
    }
  },
  { immediate: true, deep: true }
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm()
      if (props.type === 'add') {
        // 新增时重置表单
        Object.assign(form, {
          userId: undefined,
          deptId: undefined,
          userName: '',
          nickName: '',
          password: '',
          phonenumber: '',
          email: '',
          sex: '0',
          status: '0',
          remark: '',
          postIds: [],
          roleIds: []
        })
      }
    }
  }
)

/**
 * 获取用户详细信息
 */
async function getUserDetail(userId: number) {
  try {
    const response = await UserApi.getUser(userId)
    const { data, posts, roles, postIds, roleIds } = response.data
    Object.assign(form, data)
    form.postIds = postIds
    form.roleIds = roleIds
    postOptions.value = posts
    roleOptions.value = roles
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}

/**
 * 提交表单
 */
async function submitForm() {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (props.type === 'add') {
      await UserApi.addUser(form)
      ElMessage.success('新增成功')
    } else {
      await UserApi.updateUser(form)
      ElMessage.success('修改成功')
    }
    
    emit('update:visible', false)
    emit('submit')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

/**
 * 取消操作
 */
function cancel() {
  emit('update:visible', false)
}

/**
 * 重置表单
 */
function resetForm() {
  formRef.value?.resetFields()
}

/**
 * 获取部门树数据
 */
async function getDeptTree() {
  try {
    const response = await UserApi.deptTreeSelect()
    deptOptions.value = response.data || []
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

// 组件挂载时获取部门树数据
onMounted(() => {
  getDeptTree()
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: center;
}
</style>
