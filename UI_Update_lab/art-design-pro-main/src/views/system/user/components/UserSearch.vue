<template>
  <el-card shadow="never" class="search-card">
    <el-form
      ref="queryRef"
      :model="queryForm"
      :inline="true"
      label-width="68px"
      class="search-form"
    >
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryForm.userName"
          placeholder="请输入用户名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryForm.phonenumber"
          placeholder="请输入手机号码"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryForm.status"
          placeholder="用户状态"
          clearable
          style="width: 240px"
        >
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { FormInstance } from 'element-plus'

interface SearchForm {
  userName: string
  phonenumber: string
  status: string
  beginTime: string
  endTime: string
}

interface Props {
  modelValue: SearchForm
}

interface Emits {
  (e: 'update:modelValue', value: SearchForm): void
  (e: 'search'): void
  (e: 'reset'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const queryRef = ref<FormInstance>()
const dateRange = ref<[string, string] | []>([])

// 查询表单
const queryForm = reactive<SearchForm>({
  userName: '',
  phonenumber: '',
  status: '',
  beginTime: '',
  endTime: ''
})

// 监听props变化
watch(
  () => props.modelValue,
  (newVal) => {
    Object.assign(queryForm, newVal)
  },
  { immediate: true, deep: true }
)

// 监听表单变化
watch(
  queryForm,
  (newVal) => {
    emit('update:modelValue', { ...newVal })
  },
  { deep: true }
)

/**
 * 处理日期范围变化
 */
function handleDateChange(dates: [string, string] | null) {
  if (dates && dates.length === 2) {
    queryForm.beginTime = dates[0]
    queryForm.endTime = dates[1]
  } else {
    queryForm.beginTime = ''
    queryForm.endTime = ''
  }
}

/**
 * 搜索按钮操作
 */
function handleQuery() {
  emit('search')
}

/**
 * 重置按钮操作
 */
function resetQuery() {
  queryRef.value?.resetFields()
  dateRange.value = []
  queryForm.userName = ''
  queryForm.phonenumber = ''
  queryForm.status = ''
  queryForm.beginTime = ''
  queryForm.endTime = ''
  emit('reset')
}
</script>

<style lang="scss" scoped>
.search-card {
  margin-bottom: 16px;

  :deep(.el-card__body) {
    padding-bottom: 2px;
  }
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
